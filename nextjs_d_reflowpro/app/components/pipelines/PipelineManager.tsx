'use client';

import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import { authService } from '../../services/auth';
import { Card } from '../ui/Card';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import { Alert } from '../ui/Alert';
import { 
  Play, 
  Pause, 
  StopCircle,
  Edit,
  Trash2,
  Plus,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  Database,
  ArrowRight
} from 'lucide-react';

interface Pipeline {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'running' | 'failed';
  created_at: string;
  updated_at: string;
  last_execution_at: string | null;
  execution_count: number;
  success_rate: number;
  config: {
    source: string;
    destination: string;
    transformations: string[];
    schedule?: string;
  };
}

interface PipelineExecution {
  id: string;
  pipeline_id: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  started_at: string;
  completed_at: string | null;
  rows_processed: number;
  error_message: string | null;
}

const PipelineManager: React.FC = () => {
  const [pipelines, setPipelines] = useState<Pipeline[]>([]);
  const [executions, setExecutions] = useState<Record<string, PipelineExecution[]>>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPipeline, setSelectedPipeline] = useState<string | null>(null);
  const [showCreateForm, setShowCreateForm] = useState(false);

  const fetchPipelines = async () => {
    try {
      setError(null);

      // Check if user has valid authentication before making API calls
      if (!authService.hasValidAuthentication()) {
        console.log('No valid authentication found, user needs to log in');
        setError('Please log in to view pipelines.');
        setPipelines([]);
        setLoading(false);
        return;
      }

      const response = await apiService.getPipelines();
      const pipelineData = response.pipelines || [];
      setPipelines(pipelineData);
      
      // Fetch executions for each pipeline
      const executionPromises = pipelineData.map(async (pipeline: Pipeline) => {
        const pipelineExecutions = await apiService.getPipelineExecutions(pipeline.id);
        return { pipelineId: pipeline.id, executions: pipelineExecutions };
      });
      
      const executionResults = await Promise.all(executionPromises);
      const executionMap: Record<string, PipelineExecution[]> = {};
      executionResults.forEach(({ pipelineId, executions }) => {
        executionMap[pipelineId] = executions.slice(0, 5); // Keep latest 5 executions
      });
      setExecutions(executionMap);
      
    } catch (err: unknown) {
      console.error('Pipeline fetch error:', err);

      // Handle authentication errors specifically
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      if (errorMessage.includes('Session expired') || errorMessage.includes('Authentication failed')) {
        setError('Your session has expired. Please refresh the page and log in again.');
      } else if (errorMessage.includes('Token refresh failed')) {
        setError('Authentication error. Please refresh the page and try again.');
      } else {
        setError(errorMessage || 'Failed to load pipelines');
      }

      setPipelines([]); // Ensure pipelines is always an array
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPipelines();
    
    // Auto-refresh every 2 minutes - reduced from 30s to improve performance
    const interval = setInterval(fetchPipelines, 120000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'text-green-600';
      case 'running':
        return 'text-blue-600';
      case 'failed':
        return 'text-red-600';
      case 'pending':
        return 'text-yellow-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'running':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-600" />;
      case 'pending':
        return <AlertCircle className="w-4 h-4 text-yellow-600" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-600" />;
    }
  };

  const executePipeline = async (pipelineId: string) => {
    try {
      await apiService.executePipeline(pipelineId);
      // Refresh pipelines to show updated status
      await fetchPipelines();
    } catch (err) {
      console.error('Pipeline execution error:', err);
      setError('Failed to execute pipeline');
    }
  };

  const deletePipeline = async (pipelineId: string) => {
    if (!confirm('Are you sure you want to delete this pipeline?')) return;
    
    try {
      await apiService.deletePipeline(pipelineId);
      await fetchPipelines();
    } catch (err) {
      console.error('Pipeline deletion error:', err);
      setError('Failed to delete pipeline');
    }
  };

  const cancelExecution = async (pipelineId: string, executionId: string) => {
    try {
      await apiService.cancelExecution(pipelineId, executionId);
      await fetchPipelines();
    } catch (err) {
      console.error('Execution cancellation error:', err);
      setError('Failed to cancel execution');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <LoadingSpinner size="lg" />
        <span className="ml-2 text-gray-600">Loading pipelines...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive" title="Pipeline Error">
          {error}
        </Alert>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">ETL Pipelines</h2>
          <p className="text-gray-600">Manage and monitor your data transformation pipelines</p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Create Pipeline</span>
        </button>
      </div>

      {/* Pipeline Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Database className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">{pipelines.length}</div>
              <div className="text-sm text-gray-500">Total Pipelines</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Play className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {pipelines.filter(p => p.status === 'active').length}
              </div>
              <div className="text-sm text-gray-500">Active</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <Clock className="w-8 h-8 text-blue-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {pipelines.filter(p => p.status === 'running').length}
              </div>
              <div className="text-sm text-gray-500">Running</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-4">
          <div className="flex items-center space-x-3">
            <CheckCircle className="w-8 h-8 text-green-600" />
            <div>
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(pipelines.reduce((sum, p) => sum + p.success_rate, 0) / pipelines.length) || 0}%
              </div>
              <div className="text-sm text-gray-500">Avg Success Rate</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Pipeline List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {pipelines.map((pipeline) => (
          <Card key={pipeline.id} className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{pipeline.name}</h3>
                <p className="text-gray-600 text-sm">{pipeline.description}</p>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusIcon(pipeline.status)}
                <span className={`text-sm font-medium ${getStatusColor(pipeline.status)}`}>
                  {pipeline.status.toUpperCase()}
                </span>
              </div>
            </div>

            {/* Pipeline Config */}
            <div className="mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <span className="font-medium">{pipeline.config.source}</span>
                <ArrowRight className="w-4 h-4" />
                <span>{pipeline.config.transformations.length} transformations</span>
                <ArrowRight className="w-4 h-4" />
                <span className="font-medium">{pipeline.config.destination}</span>
              </div>
              {pipeline.config.schedule && (
                <div className="text-xs text-gray-500 mt-1">
                  Schedule: {pipeline.config.schedule}
                </div>
              )}
            </div>

            {/* Pipeline Stats */}
            <div className="grid grid-cols-3 gap-4 mb-4 text-center">
              <div>
                <div className="text-xl font-bold text-gray-900">{pipeline.execution_count}</div>
                <div className="text-xs text-gray-500">Executions</div>
              </div>
              <div>
                <div className="text-xl font-bold text-green-600">{pipeline.success_rate}%</div>
                <div className="text-xs text-gray-500">Success Rate</div>
              </div>
              <div>
                <div className="text-xl font-bold text-gray-900">
                  {pipeline.last_execution_at 
                    ? new Date(pipeline.last_execution_at).toLocaleDateString()
                    : 'Never'
                  }
                </div>
                <div className="text-xs text-gray-500">Last Run</div>
              </div>
            </div>

            {/* Recent Executions */}
            {executions[pipeline.id] && executions[pipeline.id].length > 0 && (
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Recent Executions</h4>
                <div className="space-y-2">
                  {executions[pipeline.id].map((execution) => (
                    <div key={execution.id} className="flex justify-between items-center text-xs">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(execution.status)}
                        <span>{new Date(execution.started_at).toLocaleString()}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-gray-600">{execution.rows_processed.toLocaleString()} rows</span>
                        {execution.status === 'running' && (
                          <button
                            onClick={() => cancelExecution(pipeline.id, execution.id)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <StopCircle className="w-3 h-3" />
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-between items-center">
              <div className="flex space-x-2">
                <button
                  onClick={() => executePipeline(pipeline.id)}
                  disabled={pipeline.status === 'running'}
                  className="bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                >
                  <Play className="w-3 h-3" />
                  <span>Run</span>
                </button>
                <button
                  onClick={() => setSelectedPipeline(pipeline.id)}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center space-x-1"
                >
                  <Edit className="w-3 h-3" />
                  <span>Edit</span>
                </button>
              </div>
              <button
                onClick={() => deletePipeline(pipeline.id)}
                className="text-red-600 hover:text-red-800 p-1"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </Card>
        ))}
      </div>

      {pipelines.length === 0 && !loading && (
        <Card className="p-8 text-center">
          <Database className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Pipelines Found</h3>
          <p className="text-gray-600 mb-4">Create your first ETL pipeline to get started with data processing.</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
          >
            Create Your First Pipeline
          </button>
        </Card>
      )}
    </div>
  );
};

export default PipelineManager;